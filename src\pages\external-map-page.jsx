import { useOutletContext } from "react-router";
import ExternalMapView from "../components/map/external-map-view";
import { useEffect, useRef, useState } from "react";
import Map from "react-map-gl/mapbox";

export default function ExternalMapPage() {
  const { sidebarOpen, theme } = useOutletContext();
  const [center, setCenter] = useState();
  const [mapStyle, setMapStyle] = useState(
    theme === "light"
      ? "mapbox://styles/mapbox/light-v10"
      : "mapbox://styles/mapbox/dark-v10"
  );
  const mapRef = useRef();

  useEffect(() => {
    if (mapRef.current) mapRef.current.resize();
  }, [sidebarOpen]);

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/mapbox/light-v10"
        : "mapbox://styles/mapbox/dark-v10"
    );
  }, [theme]);

  return (
    <>
      <div
        style={{
          flex: 1,
          borderTop: `1px solid ${
            theme === "light" ? "#ccc" : "rgba(255, 255, 255, 0.2)"
          }`,
          display: "flex",
        }}
      >
        <div style={{ flex: 1 }}>
          <Map
            ref={mapRef}
            initialViewState={{ longitude: 0, latitude: 0, zoom: 1 }}
            onLoad={(target) => {
              mapRef.current = target;
              setCenter(target.target.getCenter());
            }}
            onMove={(target) => {
              setCenter(target.target.getCenter());
            }}
            mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
            mapStyle={mapStyle}
          ></Map>
        </div>
        <div style={{ width: 300, padding: 10 }}></div>
      </div>
    </>
  );
}
