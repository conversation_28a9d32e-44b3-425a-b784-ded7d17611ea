import { Button, Colors, FormGroup, InputGroup } from "@blueprintjs/core";
import { functions } from "../services/functions";

export default function GlobalSigninPage() {
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "100svh",
        backgroundColor: Colors.LIGHT_GRAY5,
      }}
    >
      <div style={{ width: 300 }}>
        <div style={{ marginBottom: 30 }}>
          <div style={{ fontSize: 18, fontWeight: 700 }}>Welcome back!</div>
          <div>Add your credentials to get started</div>
        </div>
        <form onSubmit={(e) => functions.auth.signin(e)}>
          <FormGroup label="Email Address">
            <InputGroup name="email" />
          </FormGroup>
          <FormGroup
            label="Password"
            helperText={
              <div style={{ textAlign: "right" }}>
                <span style={{ cursor: "pointer" }}>Forgot password?</span>
              </div>
            }
          >
            <InputGroup name="password" type="password" />
          </FormGroup>
          <Button
            type="submit"
            intent="primary"
            text="Sign In"
            fill
            style={{ marginTop: 30 }}
          />
        </form>
      </div>
    </div>
  );
}
