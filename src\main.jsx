import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router";
import GlobalContext from "./global-context.jsx";
import App from "./App.jsx";

import "normalize.css";
import "@blueprintjs/core/lib/css/blueprint.css";
import "@blueprintjs/icons/lib/css/blueprint-icons.css";
import "mapbox-gl/dist/mapbox-gl.css";
import "./custom.css";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <GlobalContext>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </GlobalContext>
  </StrictMode>
);
