import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "./firebase";
import { OverlayToaster } from "@blueprintjs/core";

export const functions = {
  auth: {
    signin: async (event) => {
      event.preventDefault();

      const toaster = await OverlayToaster.create({});
      signInWithEmailAndPassword(
        auth,
        event.target.email.value,
        event.target.password.value
      )
        .then(() => {})
        .catch((error) => {
          toaster.show({
            message: error.message,
            intent: "danger",
            icon: "warning-sign",
          });
        });
    },
  },
};
