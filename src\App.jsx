import { Navigate, Route, Routes } from "react-router";
import { globalContext } from "./global-context";
import { useContext } from "react";
import GlobalSigninPage from "./pages/global-signin-page";
import CommonLayout from "./components/layout/common-layout";
import ExternalLayout from "./components/layout/external-layout.jsx";
import ExternalMapPage from "./pages/external-map-page.jsx";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route>
        <Route path="*" element={<Navigate to="/" />} />
        {loged ? (
          <Route>
            <Route path="doc" element={"Documentation"}>
              <Route path="" element={"Documentation"} />
            </Route>
            <Route element={<CommonLayout />}>
              <Route element={<ExternalLayout />}>
                <Route path="" element={"Dashboard"} />
                <Route path="map" element={<ExternalMapPage />} />
              </Route>
              <Route path="internal">
                <Route path="" element={"Dashboard"} />
              </Route>
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={"Public"} />
            <Route path="signin" element={<GlobalSigninPage />} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;
