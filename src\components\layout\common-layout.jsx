import {
  Button,
  Colors,
  EntityTitle,
  Menu,
  MenuDivider,
  MenuItem,
  Popover,
} from "@blueprintjs/core";
import { useState } from "react";
import { auth } from "../../services/firebase";
import dayjs from "dayjs";
import { Outlet } from "react-router";
import Logo from "../assets/logo";

export default function CommonLayout() {
  const [theme, setTheme] = useState("light");
  const [sidebarOpen, setSidebarOpen] = useState(true);
  return (
    <>
      <div
        className={theme === "light" ? "light" : "bp6-dark"}
        style={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100svh",
          maxHeight: "100svh",
          overflow: "hidden",
          backgroundColor: theme === "light" ? "white" : Colors.DARK_GRAY2,
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div
            style={{
              height: 50,
              width: sidebarOpen ? 230 : 50,
              display: "flex",
              alignItems: "center",
              paddingInline: 15,
              borderRight: `1px solid ${
                theme === "light"
                  ? Colors.LIGHT_GRAY1
                  : "rgba(255, 255, 255, 0.2)"
              }`,
              justifyContent: "space-between",
            }}
          >
            {sidebarOpen && <Logo />}
            <Button
              icon={sidebarOpen ? "menu-closed" : "menu-open"}
              size="small"
              variant="minimal"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            />
          </div>
          <div
            style={{
              flex: 1,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              paddingInline: "40px 15px",
              height: 50,
            }}
          >
            <div>{dayjs().format("dddd DD MMMM YYYY").toLocaleUpperCase()}</div>
            <div style={{ display: "flex" }}>
              <Button
                icon={theme === "light" ? "moon" : "flash"}
                variant="minimal"
                onClick={() => setTheme(theme === "light" ? "dark" : "light")}
              />
              <Button icon="cog" variant="minimal" />
              <Button icon="notifications" variant="minimal" />
              <Popover
                placement="bottom-end"
                content={
                  <Menu style={{ minWidth: 230 }}>
                    <MenuDivider
                      title={
                        <EntityTitle
                          title={`${"FirstName"} ${"LastName"}`}
                          subtitle={auth.currentUser.email}
                          fill
                        />
                      }
                    />
                    <MenuDivider />
                    <MenuItem text="Account settings" icon="user" />
                    <MenuItem text="Updtates" icon="updated" />
                    <MenuItem text="Documentation" icon="book" />
                    <MenuItem text="Support" icon="help" />
                    <MenuDivider />
                    <MenuItem
                      text="Log out"
                      icon="log-out"
                      intent="danger"
                      onClick={() => auth.signOut()}
                    />
                  </Menu>
                }
              >
                <Button icon="user" variant="minimal" />
              </Popover>
            </div>
          </div>
        </div>
        <div style={{ flex: 1, overflow: "hidden", display: "flex" }}>
          <Outlet context={{ sidebarOpen, theme }} />
        </div>
      </div>
    </>
  );
}
