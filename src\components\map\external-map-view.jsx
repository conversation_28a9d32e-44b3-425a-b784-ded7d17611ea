import { useEffect, useState } from "react";
import Map from "react-map-gl/mapbox";
import { useOutletContext } from "react-router";

export default function ExternalMapView({ mapRef, setCenter }) {
  const { theme, sidebarOpen } = useOutletContext();
  const [mapStyle, setMapStyle] = useState(
    theme === "light"
      ? "mapbox://styles/mapbox/light-v10"
      : "mapbox://styles/mapbox/dark-v10"
  );

  useEffect(() => {
    mapRef.current?.resize();
  }, [sidebarOpen]);

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/mapbox/light-v10"
        : "mapbox://styles/mapbox/dark-v10"
    );
  }, [theme]);

  return (
    <>
      <Map
        ref={mapRef}
        onLoad={(target) => {
          mapRef = target;
          setCenter(target.getCenter());
        }}
        onMove={(target) => {
          mapRef = target;
          setCenter(target.getCenter());
        }}
        initialViewState={{ longitude: 0, latitude: 0, zoom: 1 }}
        mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
        mapStyle={mapStyle}
      ></Map>
    </>
  );
}
