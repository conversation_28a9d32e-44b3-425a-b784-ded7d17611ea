import { Colors, Menu, MenuItem, Tag } from "@blueprintjs/core";
import { Outlet, useNavigate, useOutletContext } from "react-router";

export default function ExternalLayout() {
  const navigate = useNavigate();
  const { sidebarOpen, theme } = useOutletContext();

  return (
    <>
      <div
        style={{
          width: sidebarOpen ? 230 : 50,
          borderRight: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : "rgba(255, 255, 255, 0.2)"
          }`,
          paddingBlock: 10,
          overflow: "hidden",
        }}
      >
        <Menu
          style={{ padding: 0, backgroundColor: "transparent" }}
          className="sidebarMenu"
        >
          <MenuItem
            title="Dashboard"
            text={sidebarOpen && "Dashboard"}
            icon="dashboard"
            intent={window.location.pathname === "/" ? "primary" : "none"}
            onClick={() => navigate("")}
          />
          <MenuItem
            title="Map view"
            text={sidebarOpen && "Map view"}
            icon="map"
            intent={window.location.pathname === "/map" ? "primary" : "none"}
            onClick={() => navigate("map")}
          />
          <MenuItem
            title="Entities"
            text={sidebarOpen && "Entities"}
            icon="office"
            label={<Tag minimal>{0}</Tag>}
            intent={
              window.location.pathname === "/entities" ? "primary" : "none"
            }
            onClick={() => navigate("entities")}
          />
          <MenuItem
            title="Sites"
            text={sidebarOpen && "Sites"}
            icon="map-marker"
            intent={window.location.pathname === "/sites" ? "primary" : "none"}
            onClick={() => navigate("sites")}
            label={<Tag minimal>{0}</Tag>}
          />
          <MenuItem
            title="Connections"
            text={sidebarOpen && "connections"}
            icon="route"
            intent={
              window.location.pathname === "/connections" ? "primary" : "none"
            }
            onClick={() => navigate("connections")}
          />
          <MenuItem
            title="Activities"
            text={sidebarOpen && "Activities"}
            icon="briefcase"
            label={<Tag minimal>{0}</Tag>}
            intent={
              window.location.pathname === "/activities" ? "primary" : "none"
            }
            onClick={() => navigate("activities")}
          />
          <MenuItem
            title="Issues"
            text={sidebarOpen && "Issues"}
            icon="issue"
            intent={window.location.pathname === "/issues" ? "primary" : "none"}
            onClick={() => navigate("issues")}
          />
        </Menu>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "auto",
          display: "flex",
          flexFlow: "column",
        }}
      >
        <Outlet
          context={{
            sidebarOpen,
            theme,
          }}
        />
      </div>
    </>
  );
}
