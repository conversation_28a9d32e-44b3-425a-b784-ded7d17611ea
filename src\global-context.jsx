/* eslint-disable react-refresh/only-export-components */
import { onAuthStateChanged } from "firebase/auth";
import { createContext, useState } from "react";
import { auth } from "./services/firebase";
import GlobalLoader from "./components/elements/global-loader";

export const globalContext = createContext();

export default function GlobalContext(props) {
  const [loged, setLoged] = useState(null);

  onAuthStateChanged(auth, (u) => {
    if (u) {
      setLoged(true);
    }
    if (!u) {
      setLoged(false);
    }
  });

  if (loged === null) return <GlobalLoader />;
  return (
    <globalContext.Provider value={{ loged }}>
      {props.children}
    </globalContext.Provider>
  );
}
